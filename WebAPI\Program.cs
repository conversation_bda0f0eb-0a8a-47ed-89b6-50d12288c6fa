
using Application.Extensions;
using FluentValidation;
using Persistence.Extensions;
using WebAPI.Extensions;

namespace WebAPI
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Add services to the container.
            builder.Services.AddControllers();

            // Add FluentValidation
            builder.Services.AddValidatorsFromAssemblyContaining<Program>();

            // Add layer services
            builder.Services.AddApplicationServices();
            builder.Services.AddPersistenceServices(builder.Configuration);
            builder.Services.AddInfrastructureServices();

            // Add Identity and Authentication
            builder.Services.AddIdentityServices(builder.Configuration);

            // Add API documentation
            builder.Services.AddSwaggerServices();

            // Add CORS policy
            builder.Services.AddCorsServices();

            var app = builder.Build();

            // Seed Database
            await SeedDatabaseAsync(app);

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Base Project Clean Architecture API V1");
                    c.RoutePrefix = string.Empty; // Set Swagger UI at the app's root
                });
            }

            app.UseHttpsRedirection();

            app.UseCors("AllowAll");

            app.UseAuthentication();
            app.UseAuthorization();

            app.MapControllers();

            app.Run();
        }

        private static async Task SeedDatabaseAsync(WebApplication app)
        {
            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;

            try
            {
                var context = services.GetRequiredService<Persistence.Context.ApplicationDbContext>();
                var userManager = services.GetRequiredService<Microsoft.AspNetCore.Identity.UserManager<Domain.Entities.ApplicationUser>>();
                var roleManager = services.GetRequiredService<Microsoft.AspNetCore.Identity.RoleManager<Domain.Entities.ApplicationRole>>();

                await Persistence.Seeders.DatabaseSeeder.SeedAsync(context, userManager, roleManager);
            }
            catch (Exception ex)
            {
                var logger = services.GetRequiredService<ILogger<Program>>();
                logger.LogError(ex, "An error occurred while seeding the database.");
            }
        }
        }
    }
}
