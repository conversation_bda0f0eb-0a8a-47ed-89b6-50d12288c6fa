using Application.Features.Auth.Response;
using Domain.Entities;
using System.Security.Claims;

namespace Application.Common.Interfaces
{
    public interface IJwtService
    {
        Task<AuthResponse> GenerateTokenAsync(ApplicationUser user);
        Task<AuthResponse> RefreshTokenAsync(string accessToken, string refreshToken);
        ClaimsPrincipal? GetPrincipalFromExpiredToken(string token);
        string GenerateRefreshToken();
    }
}
