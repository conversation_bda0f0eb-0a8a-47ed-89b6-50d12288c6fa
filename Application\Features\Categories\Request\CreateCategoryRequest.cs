using System.ComponentModel.DataAnnotations;

namespace Application.Features.Categories.Request
{
    public class CreateCategoryRequest
    {
        [Required]
        [StringLength(100, MinimumLength = 1)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
    }
}